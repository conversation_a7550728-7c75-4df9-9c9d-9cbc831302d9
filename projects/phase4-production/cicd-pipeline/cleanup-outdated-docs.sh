#!/bin/bash

# =============================================================================
# 过期文档清理脚本
# 识别并清理过时的文档文件和空目录，避免混淆
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}                    过期文档清理工具${NC}"
echo -e "${BLUE}==============================================================================${NC}"

# 记录清理的项目
CLEANED_ITEMS=()
REMOVED_DIRS=()
REMOVED_FILES=()

# 清理函数
cleanup_empty_dir() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ] && [ -z "$(ls -A "$dir" 2>/dev/null)" ]; then
        echo -e "${YELLOW}🗂️  清理空目录: $description${NC}"
        echo "   路径: $dir"
        rm -rf "$dir"
        REMOVED_DIRS+=("$dir")
        CLEANED_ITEMS+=("空目录: $description")
        echo -e "${GREEN}   ✅ 已删除${NC}"
        echo
    fi
}

# 清理过期文件
cleanup_outdated_file() {
    local file="$1"
    local description="$2"
    local reason="$3"
    
    if [ -f "$file" ]; then
        echo -e "${YELLOW}📄 清理过期文件: $description${NC}"
        echo "   路径: $file"
        echo "   原因: $reason"
        rm -f "$file"
        REMOVED_FILES+=("$file")
        CLEANED_ITEMS+=("过期文件: $description")
        echo -e "${GREEN}   ✅ 已删除${NC}"
        echo
    fi
}

# 备份重要文件
backup_file() {
    local file="$1"
    if [ -f "$file" ]; then
        cp "$file" "${file}.backup.$(date +%Y%m%d_%H%M%S)"
        echo -e "${BLUE}💾 已备份: $file${NC}"
    fi
}

echo -e "${YELLOW}📋 开始扫描过期文档和空目录...${NC}"
echo

# 1. 保留GitHub Actions目录（包含模板和脚本）
# GitHub Actions目录现在包含有用的模板和脚本，不应删除

# 2. 清理空的GitLab CI脚本目录
cleanup_empty_dir "projects/phase4-production/cicd-pipeline/gitlab-ci/scripts" "GitLab CI脚本目录"

# 3. 清理空的GitLab CI目录（如果scripts删除后为空）
cleanup_empty_dir "projects/phase4-production/cicd-pipeline/gitlab-ci" "GitLab CI配置目录"

# 4. 清理空的安全加固目录
cleanup_empty_dir "projects/phase4-production/security-hardening" "安全加固实践目录"

# 5. 清理空的最终项目目录
cleanup_empty_dir "projects/phase4-production/final-project" "综合最终项目目录"

# 6. 检查并处理重复的文档
echo -e "${YELLOW}📄 检查重复和过期的文档文件...${NC}"

# 检查GETTING_STARTED.md是否与README.md重复
if [ -f "projects/phase4-production/GETTING_STARTED.md" ] && [ -f "projects/phase4-production/README.md" ]; then
    echo -e "${YELLOW}🔍 发现重复文档: GETTING_STARTED.md 与 README.md${NC}"
    echo "   分析内容重复度..."
    
    # 简单的重复度检查（检查相似的标题和关键词）
    common_lines=$(grep -Fx -f "projects/phase4-production/README.md" "projects/phase4-production/GETTING_STARTED.md" 2>/dev/null | wc -l || echo "0")
    total_lines=$(wc -l < "projects/phase4-production/GETTING_STARTED.md" || echo "1")
    
    if [ "$common_lines" -gt 10 ]; then
        backup_file "projects/phase4-production/GETTING_STARTED.md"
        cleanup_outdated_file "projects/phase4-production/GETTING_STARTED.md" "入门指南文档" "内容与README.md重复，信息已整合"
    fi
fi

# 7. 清理过期的部署策略目录（如果为空）
cleanup_empty_dir "projects/phase4-production/cicd-pipeline/deployment-strategies" "部署策略实践目录"

# 8. 检查是否有其他空目录
echo -e "${YELLOW}🔍 扫描其他可能的空目录...${NC}"

# 查找空目录
find projects/phase4-production -type d -empty 2>/dev/null | while read -r empty_dir; do
    if [ -n "$empty_dir" ]; then
        cleanup_empty_dir "$empty_dir" "发现的空目录"
    fi
done

# 9. 生成清理报告
echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}                           清理报告${NC}"
echo -e "${BLUE}==============================================================================${NC}"

if [ ${#CLEANED_ITEMS[@]} -eq 0 ]; then
    echo -e "${GREEN}🎉 没有发现需要清理的过期文档或空目录！${NC}"
    echo -e "${GREEN}   项目文档结构良好，无需清理。${NC}"
else
    echo -e "${GREEN}✅ 清理完成！共处理 ${#CLEANED_ITEMS[@]} 个项目：${NC}"
    echo
    
    for item in "${CLEANED_ITEMS[@]}"; do
        echo -e "${GREEN}  ✓ $item${NC}"
    done
    
    echo
    echo -e "${BLUE}📊 详细统计：${NC}"
    echo -e "${BLUE}  删除目录: ${#REMOVED_DIRS[@]} 个${NC}"
    echo -e "${BLUE}  删除文件: ${#REMOVED_FILES[@]} 个${NC}"
    
    if [ ${#REMOVED_DIRS[@]} -gt 0 ]; then
        echo
        echo -e "${YELLOW}🗂️  已删除的目录：${NC}"
        for dir in "${REMOVED_DIRS[@]}"; do
            echo "    $dir"
        done
    fi
    
    if [ ${#REMOVED_FILES[@]} -gt 0 ]; then
        echo
        echo -e "${YELLOW}📄 已删除的文件：${NC}"
        for file in "${REMOVED_FILES[@]}"; do
            echo "    $file"
        done
    fi
fi

echo
echo -e "${BLUE}💡 建议的后续操作：${NC}"
echo "  1. 检查Git状态: git status"
echo "  2. 提交清理变更: git add . && git commit -m 'cleanup: 清理过期文档和空目录'"
echo "  3. 更新项目文档以反映当前结构"
echo "  4. 如有备份文件，确认无误后可删除"

echo
echo -e "${GREEN}🎯 清理完成！项目文档结构现在更加清晰。${NC}"

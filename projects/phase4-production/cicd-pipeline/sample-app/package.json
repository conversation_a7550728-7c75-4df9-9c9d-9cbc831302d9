{"name": "sample-cicd-app", "version": "1.0.0", "description": "Sample application for CI/CD pipeline practice", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["nodejs", "express", "cicd", "kubernetes", "docker"], "author": "Cloud Native Learner", "license": "MIT", "dependencies": {"express": "^4.18.2"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/sample-app
  NODE_VERSION: '18'

jobs:
  # 测试作业
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16, 18, 20]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: sample-app/package-lock.json
    
    - name: Install dependencies
      run: |
        cd sample-app
        npm ci
    
    - name: Run linting
      run: |
        cd sample-app
        npm run lint || echo "Linting completed with warnings"
    
    - name: Run tests
      run: |
        cd sample-app
        npm run test:coverage
    
    - name: Upload coverage to Codecov
      if: matrix.node-version == 18
      uses: codecov/codecov-action@v3
      with:
        file: ./sample-app/coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # 安全扫描作业
  security:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: sample-app/package-lock.json
    
    - name: Install dependencies
      run: |
        cd sample-app
        npm ci
    
    - name: Run security audit
      run: |
        cd sample-app
        npm audit --audit-level=high
      continue-on-error: true
    
    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --file=sample-app/package.json
      continue-on-error: true

  # 构建和推送镜像
  build-and-push:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./sample-app
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到Staging环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: http://staging.sample-app.local
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > /tmp/kubeconfig
        echo "KUBECONFIG=/tmp/kubeconfig" >> $GITHUB_ENV
    
    - name: Deploy to staging
      run: |
        kubectl create namespace staging --dry-run=client -o yaml | kubectl apply -f -
        sed -e "s|image: sample-app:latest|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}|g" \
            -e "s|APP_VERSION.*|APP_VERSION\n          value: \"${{ github.sha }}\"|g" \
            sample-app/k8s/deployment.yaml | kubectl apply -n staging -f -
        kubectl rollout status deployment/sample-app -n staging --timeout=300s
    
    - name: Verify deployment
      run: |
        kubectl get pods -n staging
        kubectl get services -n staging

  # 部署到Production环境（手动触发）
  deploy-production:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: http://sample-app.local
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > /tmp/kubeconfig
        echo "KUBECONFIG=/tmp/kubeconfig" >> $GITHUB_ENV
    
    - name: Deploy to production
      run: |
        kubectl create namespace production --dry-run=client -o yaml | kubectl apply -f -
        sed -e "s|image: sample-app:latest|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}|g" \
            -e "s|APP_VERSION.*|APP_VERSION\n          value: \"${{ github.sha }}\"|g" \
            sample-app/k8s/deployment.yaml | kubectl apply -n production -f -
        kubectl rollout status deployment/sample-app -n production --timeout=300s
    
    - name: Verify deployment
      run: |
        kubectl get pods -n production
        kubectl get services -n production
    
    - name: Post deployment notification
      if: success()
      run: |
        echo "🚀 Successfully deployed to production!"
        echo "Version: ${{ github.sha }}"
        echo "Environment: production"
